.countdown-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a2332 0%, #2d3748 50%, #1a2332 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: white;
  font-family: system-ui, -apple-system, sans-serif;
}

.countdown-header {
  text-align: center;
  margin-bottom: 4rem;
}

.countdown-title {
  font-size: 4rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.countdown-subtitle {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #ffffff;
}

.secure-text {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.launch-date {
  font-size: 1.2rem;
  color: #94a3b8;
  margin: 0;
  font-weight: 400;
}

.countdown-timer {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 4rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.time-unit {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
}

.time-number {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1;
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.time-label {
  font-size: 0.9rem;
  color: #94a3b8;
  font-weight: 500;
  margin-top: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.time-separator {
  font-size: 3rem;
  font-weight: 300;
  color: #64748b;
  margin: 0 0.5rem;
}

.countdown-footer {
  text-align: center;
  max-width: 600px;
}

.powered-by {
  font-size: 0.9rem;
  color: #94a3b8;
  margin: 0 0 1.5rem 0;
  font-weight: 500;
}

.description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #cbd5e1;
  margin: 0;
}

.highlight {
  color: #3b82f6;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .countdown-title {
    font-size: 2.5rem;
  }

  .countdown-subtitle {
    font-size: 1.8rem;
  }

  .countdown-timer {
    gap: 1rem;
    padding: 1.5rem;
    flex-wrap: wrap;
  }

  .time-number {
    font-size: 2.5rem;
  }

  .time-unit {
    min-width: 70px;
  }

  .time-separator {
    font-size: 2rem;
    margin: 0 0.25rem;
  }
}

@media (max-width: 480px) {
  .countdown-container {
    padding: 1rem;
  }

  .countdown-title {
    font-size: 2rem;
  }

  .countdown-subtitle {
    font-size: 1.5rem;
  }

  .countdown-timer {
    gap: 0.5rem;
    padding: 1rem;
  }

  .time-number {
    font-size: 2rem;
  }

  .time-unit {
    min-width: 60px;
  }

  .time-separator {
    font-size: 1.5rem;
  }
}
