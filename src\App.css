.countdown-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a2332 0%, #2d3748 50%, #1a2332 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: white;
  font-family: system-ui, -apple-system, sans-serif;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
  transition: all 1s ease-out;
}

.countdown-container.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Animated Background Particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #3b82f6, #06b6d4);
  border-radius: 50%;
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.particle-1 { top: 10%; left: 10%; animation-delay: 0s; }
.particle-2 { top: 20%; left: 80%; animation-delay: 1s; }
.particle-3 { top: 80%; left: 20%; animation-delay: 2s; }
.particle-4 { top: 60%; left: 90%; animation-delay: 3s; }
.particle-5 { top: 30%; left: 60%; animation-delay: 4s; }
.particle-6 { top: 70%; left: 70%; animation-delay: 0.5s; }
.particle-7 { top: 40%; left: 30%; animation-delay: 1.5s; }
.particle-8 { top: 90%; left: 50%; animation-delay: 2.5s; }
.particle-9 { top: 15%; left: 40%; animation-delay: 3.5s; }
.particle-10 { top: 50%; left: 15%; animation-delay: 4.5s; }
.particle-11 { top: 25%; left: 75%; animation-delay: 0.8s; }
.particle-12 { top: 75%; left: 85%; animation-delay: 1.8s; }
.particle-13 { top: 35%; left: 5%; animation-delay: 2.8s; }
.particle-14 { top: 85%; left: 35%; animation-delay: 3.8s; }
.particle-15 { top: 5%; left: 65%; animation-delay: 4.8s; }
.particle-16 { top: 65%; left: 45%; animation-delay: 0.3s; }
.particle-17 { top: 45%; left: 95%; animation-delay: 1.3s; }
.particle-18 { top: 95%; left: 25%; animation-delay: 2.3s; }
.particle-19 { top: 55%; left: 55%; animation-delay: 3.3s; }
.particle-20 { top: 12%; left: 88%; animation-delay: 4.3s; }

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
}

.countdown-header {
  text-align: center;
  margin-bottom: 4rem;
  position: relative;
  z-index: 1;
  animation: slideInFromTop 1s ease-out 0.3s both;
}

.logo-container {
  margin-bottom: 2rem;
  animation: logoEntrance 1.5s ease-out both;
}

.platform-logo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.3),
    0 0 60px rgba(6, 182, 212, 0.2),
    0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  animation: logoFloat 3s ease-in-out infinite;
}

.platform-logo:hover {
  transform: scale(1.1);
  box-shadow:
    0 0 40px rgba(59, 130, 246, 0.5),
    0 0 80px rgba(6, 182, 212, 0.3),
    0 15px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(59, 130, 246, 0.5);
}

.countdown-title {
  font-size: 4rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: glow 2s ease-in-out infinite alternate;
}

.countdown-title::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: blur(10px);
  opacity: 0.5;
  z-index: -1;
}

.countdown-subtitle {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #ffffff;
  animation: slideInFromLeft 1s ease-out 0.6s both;
}

.secure-text {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: pulse 2s ease-in-out infinite;
}

.launch-date {
  font-size: 1.2rem;
  color: #94a3b8;
  margin: 0;
  font-weight: 400;
  animation: slideInFromRight 1s ease-out 0.9s both;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes glow {
  from {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
  to {
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.6);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes logoEntrance {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-180deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2) rotate(-90deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

.countdown-timer {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 4rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
  animation: slideInFromBottom 1s ease-out 1.2s both;
  overflow: hidden;
}

.countdown-timer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

.time-unit {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
  position: relative;
}

.time-number-container {
  position: relative;
  perspective: 1000px;
  margin-bottom: 0.5rem;
}

.time-number {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1;
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  transition: transform 0.3s ease-in-out;
  display: block;
  text-align: center;
  min-height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-number.flip-animation {
  animation: flipNumber 0.6s ease-in-out;
}

.time-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: breathe 2s ease-in-out infinite;
  z-index: -1;
}

.time-label {
  font-size: 0.9rem;
  color: #94a3b8;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  animation: fadeInUp 1s ease-out 1.5s both;
}

.time-separator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0.5rem;
}

.separator-dot {
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  border-radius: 50%;
  animation: blink 2s ease-in-out infinite;
}

.separator-dot:nth-child(2) {
  animation-delay: 1s;
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes flipNumber {
  0% {
    transform: scale(1) rotateY(0deg);
  }
  50% {
    transform: scale(1.1) rotateY(180deg);
  }
  100% {
    transform: scale(1) rotateY(360deg);
  }
}

@keyframes breathe {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.6;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
    transform: scale(1);
  }
  25% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  75% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.countdown-footer {
  text-align: center;
  max-width: 600px;
  position: relative;
  z-index: 1;
  animation: fadeInUp 1s ease-out 1.8s both;
}

.powered-by {
  font-size: 0.9rem;
  color: #94a3b8;
  margin: 0 0 1.5rem 0;
  font-weight: 500;
  animation: slideInFromBottom 1s ease-out 2.1s both;
}

.description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #cbd5e1;
  margin: 0;
  animation: slideInFromBottom 1s ease-out 2.4s both;
}

.highlight {
  color: #3b82f6;
  font-weight: 600;
  position: relative;
  transition: all 0.3s ease;
}

.highlight:hover {
  color: #06b6d4;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .platform-logo {
    width: 100px;
    height: 100px;
  }

  .countdown-title {
    font-size: 2.5rem;
  }

  .countdown-subtitle {
    font-size: 1.8rem;
  }

  .countdown-timer {
    gap: 1rem;
    padding: 1.5rem;
    flex-wrap: wrap;
  }

  .time-number {
    font-size: 2.5rem;
  }

  .time-unit {
    min-width: 70px;
  }

  .time-glow {
    width: 90px;
    height: 90px;
  }

  .particles {
    display: none; /* Hide particles on mobile for better performance */
  }
}

@media (max-width: 480px) {
  .countdown-container {
    padding: 1rem;
  }

  .platform-logo {
    width: 80px;
    height: 80px;
  }

  .logo-container {
    margin-bottom: 1.5rem;
  }

  .countdown-title {
    font-size: 2rem;
  }

  .countdown-subtitle {
    font-size: 1.5rem;
  }

  .countdown-timer {
    gap: 0.5rem;
    padding: 1rem;
    grid-template-columns: repeat(2, 1fr);
    display: grid;
  }

  .time-number {
    font-size: 2rem;
  }

  .time-unit {
    min-width: 60px;
  }

  .time-glow {
    width: 70px;
    height: 70px;
  }

  .time-separator {
    display: none; /* Hide separators on very small screens */
  }
}

/* Hover effects for desktop */
@media (hover: hover) {
  .time-unit:hover .time-glow {
    animation: breathe 1s ease-in-out infinite, pulse 0.5s ease-in-out;
  }

  .time-unit:hover .time-number {
    transform: scale(1.05);
    transition: transform 0.3s ease;
  }
}
