import { useState, useEffect } from 'react'
import './App.css'
import logoCV from './assets/log-cv.jpg'

function App() {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })
  const [prevTimeLeft, setPrevTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const targetDate = new Date('2025-07-28T14:00:00').getTime()

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const difference = targetDate - now

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24))
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
        const seconds = Math.floor((difference % (1000 * 60)) / 1000)

        setPrevTimeLeft(timeLeft)
        setTimeLeft({ days, hours, minutes, seconds })
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
        clearInterval(timer)
      }
    }, 1000)

    // Set loaded after a small delay for entrance animation
    setTimeout(() => setIsLoaded(true), 100)

    return () => clearInterval(timer)
  }, [timeLeft])

  const TimeUnit = ({ value, label, prevValue }) => {
    const hasChanged = prevValue !== value

    return (
      <div className="time-unit">
        <div className="time-number-container">
          <div className={`time-number ${hasChanged ? 'flip-animation' : ''}`}>
            {value.toString().padStart(2, '0')}
          </div>
          <div className="time-glow"></div>
        </div>
        <div className="time-label">{label}</div>
      </div>
    )
  }

  return (
    <div className={`countdown-container ${isLoaded ? 'loaded' : ''}`}>
      {/* Animated background particles */}
      <div className="particles">
        {[...Array(20)].map((_, i) => (
          <div key={i} className={`particle particle-${i + 1}`}></div>
        ))}
      </div>

      <div className="countdown-header">
        <div className="logo-container">
          <img src={logoCV} alt="CV Platform Logo" className="platform-logo" />
        </div>
        <h1 className="countdown-title">Platform Launch</h1>
        <p className="countdown-subtitle">Justice Made <span className="secure-text">Secure</span></p>
        <p className="launch-date">July 28th, 2025 • 2:00 PM</p>
      </div>

      <div className="countdown-timer">
        <TimeUnit
          value={timeLeft.days}
          label="DAYS"
          prevValue={prevTimeLeft.days}
        />
        <div className="time-separator">
          <div className="separator-dot"></div>
          <div className="separator-dot"></div>
        </div>
        <TimeUnit
          value={timeLeft.hours}
          label="HOURS"
          prevValue={prevTimeLeft.hours}
        />
        <div className="time-separator">
          <div className="separator-dot"></div>
          <div className="separator-dot"></div>
        </div>
        <TimeUnit
          value={timeLeft.minutes}
          label="MINUTES"
          prevValue={prevTimeLeft.minutes}
        />
        <div className="time-separator">
          <div className="separator-dot"></div>
          <div className="separator-dot"></div>
        </div>
        <TimeUnit
          value={timeLeft.seconds}
          label="SECONDS"
          prevValue={prevTimeLeft.seconds}
        />
      </div>

      <div className="countdown-footer">
        <p className="powered-by">⚡ Powered by AI & Blockchain</p>
        <p className="description">
          Experience the future of legal services with our revolutionary platform that combines{' '}
          <span className="highlight">AI intelligence</span>, <span className="highlight">blockchain security</span>,
          and transparent processes.
        </p>
      </div>
    </div>
  )
}

export default App
