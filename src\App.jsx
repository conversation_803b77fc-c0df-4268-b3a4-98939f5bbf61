import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    const targetDate = new Date('2025-07-28T14:00:00').getTime()

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const difference = targetDate - now

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24))
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
        const seconds = Math.floor((difference % (1000 * 60)) / 1000)

        setTimeLeft({ days, hours, minutes, seconds })
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
        clearInterval(timer)
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  return (
    <div className="countdown-container">
      <div className="countdown-header">
        <h1 className="countdown-title">Platform Launch</h1>
        <p className="countdown-subtitle">Justice Made <span className="secure-text">Secure</span></p>
        <p className="launch-date">July 28th, 2025 • 2:00 PM</p>
      </div>

      <div className="countdown-timer">
        <div className="time-unit">
          <div className="time-number">{timeLeft.days.toString().padStart(2, '0')}</div>
          <div className="time-label">Days</div>
        </div>
        <div className="time-separator">:</div>
        <div className="time-unit">
          <div className="time-number">{timeLeft.hours.toString().padStart(2, '0')}</div>
          <div className="time-label">Hours</div>
        </div>
        <div className="time-separator">:</div>
        <div className="time-unit">
          <div className="time-number">{timeLeft.minutes.toString().padStart(2, '0')}</div>
          <div className="time-label">Minutes</div>
        </div>
        <div className="time-separator">:</div>
        <div className="time-unit">
          <div className="time-number">{timeLeft.seconds.toString().padStart(2, '0')}</div>
          <div className="time-label">Seconds</div>
        </div>
      </div>

      <div className="countdown-footer">
        <p className="powered-by">⚡ Powered by AI & Blockchain</p>
        <p className="description">
          Experience the future of legal services with our revolutionary platform that combines{' '}
          <span className="highlight">AI intelligence</span>, <span className="highlight">blockchain security</span>,
          and transparent processes.
        </p>
      </div>
    </div>
  )
}

export default App
